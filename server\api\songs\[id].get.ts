import { DochordParser } from '~/utils/DochordParser'

export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id')

  try {
    // เรียก API จาก dochord.com สำหรับเพลงเฉพาะ
    const dochordUrl = `https://www.dochord.com/wp-json/wp/v2/posts/${id}?_embed`

    const post = await $fetch(dochordUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    }) as any

    const webContent = await $fetch(post.link, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    }) as string

    const dochordParser = new DochordParser(webContent)
    const parsedData = dochordParser.parse()

    const transformedSong: {
      id: string
      title: string
      artist: string
      genre: string
      genreThai: string
      difficulty: string
      difficultyThai: string
      key: string
      tempo: number
      timeSignature: string
      capo: number
      strummingPattern: string
      chords: string[]
      lyrics: {
        section: string
        lines: {
          text: string
          chords: string[]
        }[]
      }[]
      tags: string[]
      description: string
      youtubeId: string
    } = {
      ...parsedData,
      id: post.id.toString(),
      title: post.title?.rendered || '',
      artist: post._embedded?.['wp:term']?.find((termGroup: any[]) =>
        termGroup.some((term: any) => term.taxonomy === 'artist'),
      )?.find((term: any) => term.taxonomy === 'artist')?.name || 'Unknown Artist',
    }

    return {
      success: true,
      data: transformedSong,
    }
  } catch (error) {
    console.error('Error fetching song from dochord.com:', error)

    throw createError({
      statusCode: 404,
      statusMessage: 'ไม่พบเพลงที่ต้องการ',
    })
  }
})
