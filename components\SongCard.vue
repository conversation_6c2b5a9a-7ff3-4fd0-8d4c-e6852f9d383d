<template>
  <div
    class="cursor-pointer overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-shadow hover:shadow-lg"
    @click="$emit('click')"
  >
    <!-- Song Header -->
    <div class="border-b border-gray-100 p-4">
      <div class="mb-2 flex items-start justify-between">
        <div class="flex-1">
          <h3 class="mb-1 text-xl font-bold text-gray-900">
            {{ song.title }}
          </h3>
          <p class="text-gray-600">
            {{ song.artist }}
          </p>
        </div>
        <span
          class="ml-2 rounded-full px-2 py-1 text-xs font-medium"
          :class="difficultyColor"
        >
          {{ song.difficultyThai }}
        </span>
      </div>

      <div class="flex items-center gap-2 text-sm text-gray-500">
        <span>{{ song.genreThai }}</span>
        <span>•</span>
        <span>{{ song.releaseYear }}</span>
        <span>•</span>
        <span>{{ song.duration }}</span>
      </div>
    </div>

    <!-- Song Info -->
    <div class="p-4">
      <div class="mb-4 grid grid-cols-2 gap-4">
        <div>
          <p class="mb-1 text-xs text-gray-500">
            Key
          </p>
          <p class="font-semibold text-blue-600">
            {{ song.key }}
          </p>
        </div>
        <div>
          <p class="mb-1 text-xs text-gray-500">
            Tempo
          </p>
          <p class="font-semibold">
            {{ song.tempo }} BPM
          </p>
        </div>
      </div>

      <!-- Chords Preview -->
      <div class="mb-4">
        <p class="mb-2 text-xs text-gray-500">
          คอร์ดที่ใช้:
        </p>
        <div class="flex flex-wrap gap-1">
          <span
            v-for="chord in song.chords.slice(0, 6)"
            :key="chord"
            class="rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
          >
            {{ chord }}
          </span>
          <span
            v-if="song.chords.length > 6"
            class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-600"
          >
            +{{ song.chords.length - 6 }}
          </span>
        </div>
      </div>

      <!-- Tags -->
      <div class="mb-4">
        <div class="flex flex-wrap gap-1">
          <span
            v-for="tag in song.tags.slice(0, 3)"
            :key="tag"
            class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700"
          >
            {{ tag }}
          </span>
        </div>
      </div>

      <!-- Description -->
      <p class="mb-4 line-clamp-2 text-sm text-gray-600">
        {{ song.description }}
      </p>

      <!-- Additional Info -->
      <div class="flex items-center justify-between text-xs text-gray-500">
        <div class="flex items-center gap-4">
          <span v-if="song.capo > 0">Capo {{ song.capo }}</span>
          <span>{{ song.timeSignature }}</span>
        </div>
        <div class="flex items-center gap-1">
          <Icon name="lucide:music" class="h-3 w-3" />
          <span>{{ song.album }}</span>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="border-t border-gray-100 bg-gray-50 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2 text-xs text-gray-500">
          <Icon name="lucide:play-circle" class="h-4 w-4" />
          <span>ดูเนื้อเพลงและคอร์ด</span>
        </div>
        <Icon name="lucide:arrow-right" class="h-4 w-4 text-gray-400" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Song {
  id: string
  title: string
  artist: string
  genre: string
  genreThai: string
  difficulty: string
  difficultyThai: string
  key: string
  tempo: number
  timeSignature: string
  capo: number
  strummingPattern: string
  chords: string[]
  tags: string[]
  description: string
  youtubeId: string
  duration: string
  releaseYear: number
  album: string
}

interface Props {
  song: Song
}

const props = defineProps<Props>()

defineEmits<{
  click: []
}>()

// Computed
const difficultyColor = computed(() => {
  const difficulty = props.song.difficulty

  switch (difficulty) {
    case 'beginner':
      return 'bg-green-100 text-green-800'
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-800'
    case 'advanced':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
