<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="border-b bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <NuxtLink
              to="/"
              class="text-2xl font-bold text-gray-900"
            >
              คอร์ด MHalong
            </NuxtLink>
          </div>
          <nav class="hidden space-x-8 md:flex">
            <NuxtLink
              to="/"
              class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              หน้าแรก
            </NuxtLink>
            <NuxtLink
              to="/search"
              class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              ค้นหาคอร์ด
            </NuxtLink>
            <NuxtLink
              to="/songs"
              class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              เพลงและคอร์ด
            </NuxtLink>
          </nav>
        </div>
      </div>
    </header>

    <!-- Loading State -->
    <div
      v-if="loading"
      class="flex items-center justify-center py-20"
    >
      <div class="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600" />
    </div>

    <!-- Error State -->
    <div
      v-else-if="error"
      class="mx-auto max-w-7xl px-4 py-20 text-center sm:px-6 lg:px-8"
    >
      <Icon
        name="lucide:alert-circle"
        class="mx-auto mb-4 h-16 w-16 text-red-500"
      />
      <h2 class="mb-2 text-2xl font-bold text-gray-900">
        ไม่พบเพลงที่ต้องการ
      </h2>
      <p class="mb-6 text-gray-600">
        {{ error }}
      </p>
      <NuxtLink
        to="/songs"
        class="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
      >
        <Icon
          name="lucide:music"
          class="mr-2 h-4 w-4"
        />
        ดูเพลงอื่น
      </NuxtLink>
    </div>

    <!-- Song Detail -->
    <div
      v-else-if="song"
      class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8"
    >
      <!-- Breadcrumb -->
      <nav class="mb-6">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
          <li>
            <NuxtLink
              to="/"
              class="hover:text-blue-600"
            >
              หน้าแรก
            </NuxtLink>
          </li>
          <li>
            <Icon
              name="lucide:chevron-right"
              class="h-4 w-4"
            />
          </li>
          <li>
            <NuxtLink
              to="/songs"
              class="hover:text-blue-600"
            >
              เพลงและคอร์ด
            </NuxtLink>
          </li>
          <li>
            <Icon
              name="lucide:chevron-right"
              class="h-4 w-4"
            />
          </li>
          <li class="font-medium text-gray-900">
            {{ song.title }}
          </li>
        </ol>
      </nav>

      <div class="grid grid-cols-1 gap-8 lg:grid-cols-4">
        <!-- Main Content -->
        <div class="lg:col-span-3">
          <!-- Song Lyrics Component -->
          <SongLyrics :song="song" />
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <!-- Song Info -->
          <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h3 class="mb-4 text-lg font-semibold text-gray-900">
              ข้อมูลเพลง
            </h3>

            <div class="space-y-3">
              <div>
                <span class="text-sm text-gray-600">อัลบั้ม:</span>
                <p class="font-medium">
                  {{ song.album }}
                </p>
              </div>

              <div>
                <span class="text-sm text-gray-600">ปีที่ออก:</span>
                <p class="font-medium">
                  {{ song.releaseYear }}
                </p>
              </div>

              <div>
                <span class="text-sm text-gray-600">ประเภท:</span>
                <p class="font-medium">
                  {{ song.genreThai }}
                </p>
              </div>

              <div>
                <span class="text-sm text-gray-600">ระยะเวลา:</span>
                <p class="font-medium">
                  {{ song.duration }}
                </p>
              </div>
            </div>
          </div>

          <!-- YouTube Video -->
          <div
            v-if="song.youtubeId"
            class="mb-6 rounded-lg bg-white p-6 shadow-sm"
          >
            <h3 class="mb-4 text-lg font-semibold text-gray-900">
              วิดีโอ
            </h3>
            <div class="aspect-video">
              <iframe
                :src="`https://www.youtube.com/embed/${song.youtubeId}`"
                class="h-full w-full rounded-lg"
                frameborder="0"
                allowfullscreen
              />
            </div>
          </div>

          <!-- Tips -->
          <div
            v-if="song.tips?.length"
            class="mb-6 rounded-lg bg-white p-6 shadow-sm"
          >
            <h3 class="mb-4 text-lg font-semibold text-gray-900">
              เทคนิคการเล่น
            </h3>
            <ul class="space-y-2">
              <li
                v-for="tip in song.tips"
                :key="tip"
                class="flex items-start"
              >
                <Icon
                  name="lucide:lightbulb"
                  class="mt-0.5 mr-2 h-4 w-4 flex-shrink-0 text-yellow-500"
                />
                <span class="text-sm text-gray-700">{{ tip }}</span>
              </li>
            </ul>
          </div>

          <!-- Related Songs -->
          <div
            v-if="song.relatedSongs?.length"
            class="rounded-lg bg-white p-6 shadow-sm"
          >
            <h3 class="mb-4 text-lg font-semibold text-gray-900">
              เพลงที่เกี่ยวข้อง
            </h3>
            <div class="space-y-2">
              <NuxtLink
                v-for="relatedSong in song.relatedSongs"
                :key="relatedSong.id"
                :to="`/song/${relatedSong.id}`"
                class="flex items-center justify-between rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div>
                  <span class="font-medium text-gray-900">{{ relatedSong.title }}</span>
                  <p class="text-sm text-gray-600">{{ relatedSong.artist }}</p>
                </div>
                <Icon
                  name="lucide:arrow-right"
                  class="h-4 w-4 text-gray-400"
                />
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Router
const route = useRoute()
const songId = route.params.id as string

// Reactive data
const loading = ref(true)
const error = ref('')
const song = ref(null)

// Fetch song data
onMounted(async () => {
  try {
    const response = await $fetch(`/api/songs/${songId}`)

    song.value = response.data

    // Update SEO
    useHead({
      title: `${song.value.title} - ${song.value.artist} | คอร์ด MHalong`,
      meta: [
        {
          name: 'description',
          content: `เนื้อเพลงและคอร์ดกีตาร์ ${song.value.title} โดย ${song.value.artist} - ${song.value.description}`,
        },
      ],
    })
  } catch (err) {
    error.value = err.statusMessage || 'เกิดข้อผิดพลาดในการโหลดข้อมูล'
  } finally {
    loading.value = false
  }
})
</script>
