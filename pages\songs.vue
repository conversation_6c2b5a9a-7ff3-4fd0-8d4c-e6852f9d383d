<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="border-b bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <NuxtLink
              to="/"
              class="text-2xl font-bold text-gray-900"
            >
              คอร์ด MHalong
            </NuxtLink>
          </div>
          <nav class="hidden space-x-8 md:flex">
            <NuxtLink
              to="/"
              class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              หน้าแรก
            </NuxtLink>
            <NuxtLink
              to="/search"
              class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              ค้นหาคอร์ด
            </NuxtLink>
            <NuxtLink
              to="/songs"
              class="rounded-md px-3 py-2 text-sm font-medium text-blue-600"
            >
              เพลงและคอร์ด
            </NuxtLink>
          </nav>
        </div>
      </div>
    </header>

    <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      <!-- Page Header -->
      <div class="mb-8 rounded-lg bg-white p-6 shadow-sm">
        <h1 class="mb-2 text-3xl font-bold text-gray-900">
          เพลงและคอร์ด
        </h1>
        <p class="text-gray-600">
          รวมเพลงฮิตพร้อมเนื้อเพลงและคอร์ดกีตาร์
        </p>
      </div>

      <!-- Search and Filters -->
      <div class="mb-8 rounded-lg bg-white p-6 shadow-sm">
        <div class="mb-4 flex flex-col gap-4 md:flex-row">
          <div class="flex-1">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="ค้นหาเพลง ศิลปิน หรือคอร์ด..."
                class="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 pr-4 pl-12 text-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none"
                @keyup.enter="performSearch"
              />
              <div class="absolute inset-y-0 left-0 flex items-center pl-4">
                <Icon
                  name="lucide:search"
                  class="h-5 w-5 text-gray-400"
                />
              </div>
            </div>
          </div>

          <button
            :disabled="loading"
            class="rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700 disabled:opacity-50"
            @click="performSearch"
          >
            <Icon
              v-if="loading"
              name="lucide:loader-2"
              class="h-5 w-5 animate-spin"
            />
            <span v-else>ค้นหา</span>
          </button>
        </div>

        <!-- Filters -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
          <div>
            <label class="mb-2 block text-sm font-medium text-gray-700">ระดับความยาก</label>
            <select
              v-model="filters.difficulty"
              class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              @change="applyFilters"
            >
              <option value="">
                ทั้งหมด
              </option>
              <option value="beginner">
                ง่าย
              </option>
              <option value="intermediate">
                ปานกลาง
              </option>
              <option value="advanced">
                ยาก
              </option>
            </select>
          </div>

          <div>
            <label class="mb-2 block text-sm font-medium text-gray-700">ประเภทเพลง</label>
            <select
              v-model="filters.genre"
              class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              @change="applyFilters"
            >
              <option value="">
                ทั้งหมด
              </option>
              <option value="Pop">
                ป็อป
              </option>
              <option value="Rock">
                ร็อค
              </option>
              <option value="Folk Rock">
                โฟล์ค ร็อค
              </option>
              <option value="Alternative Rock">
                อัลเทอร์เนทีฟ ร็อค
              </option>
            </select>
          </div>

          <div>
            <label class="mb-2 block text-sm font-medium text-gray-700">Key</label>
            <select
              v-model="filters.key"
              class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              @change="applyFilters"
            >
              <option value="">
                ทั้งหมด
              </option>
              <option value="C">
                C
              </option>
              <option value="G">
                G
              </option>
              <option value="Am">
                Am
              </option>
              <option value="Em">
                Em
              </option>
              <option value="D">
                D
              </option>
              <option value="A">
                A
              </option>
            </select>
          </div>

          <div>
            <label class="mb-2 block text-sm font-medium text-gray-700">เรียงตาม</label>
            <select
              v-model="filters.sort"
              class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              @change="applyFilters"
            >
              <option value="title">
                ชื่อเพลง
              </option>
              <option value="artist">
                ศิลปิน
              </option>
              <option value="difficulty">
                ความยาก
              </option>
              <option value="year">
                ปีที่ออก
              </option>
            </select>
          </div>
        </div>

        <div class="mt-4 flex items-center justify-between">
          <button
            class="text-sm text-gray-600 hover:text-gray-800"
            @click="clearFilters"
          >
            ล้างตัวกรอง
          </button>

          <p
            v-if="songs?.pagination"
            class="text-sm text-gray-600"
          >
            พบ {{ songs.pagination.totalCount }} เพลง
          </p>
        </div>
      </div>

      <!-- Loading State -->
      <div
        v-if="loading"
        class="flex justify-center py-12"
      >
        <div class="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600" />
      </div>

      <!-- Songs Grid -->
      <div
        v-else-if="songs?.data?.length"
        class="rounded-lg bg-white p-6 shadow-sm"
      >
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          <SongCard
            v-for="song in songs.data"
            :key="song.id"
            :song="song"
            @click="viewSong(song.id)"
          />
        </div>

        <!-- Pagination -->
        <div
          v-if="songs.pagination && songs.pagination.totalPages > 1"
          class="mt-8"
        >
          <nav class="flex justify-center">
            <div class="flex space-x-2">
              <button
                :disabled="!songs.pagination.hasPrev"
                class="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                @click="changePage(songs.pagination.currentPage - 1)"
              >
                ก่อนหน้า
              </button>

              <span class="px-3 py-2 text-sm font-medium text-gray-700">
                หน้า {{ songs.pagination.currentPage }} จาก {{ songs.pagination.totalPages }}
              </span>

              <button
                :disabled="!songs.pagination.hasNext"
                class="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                @click="changePage(songs.pagination.currentPage + 1)"
              >
                ถัดไป
              </button>
            </div>
          </nav>
        </div>
      </div>

      <!-- No Results -->
      <div
        v-else
        class="rounded-lg bg-white p-12 text-center shadow-sm"
      >
        <Icon
          name="lucide:music-off"
          class="mx-auto mb-4 h-16 w-16 text-gray-400"
        />
        <h3 class="mb-2 text-lg font-medium text-gray-900">
          ไม่พบเพลง
        </h3>
        <p class="text-gray-600">
          ลองปรับตัวกรองหรือค้นหาด้วยคำอื่น
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO
useHead({
  title: 'เพลงและคอร์ด - คอร์ด MHalong',
  meta: [
    {
      name: 'description',
      content: 'รวมเพลงฮิตพร้อมเนื้อเพลงและคอร์ดกีตาร์ ค้นหาเพลงตามศิลปิน ประเภท และระดับความยาก',
    },
  ],
})

// Reactive data
const loading = ref(true)
const songs = ref(null)
const currentPage = ref(1)
const searchQuery = ref('')
const currentQuery = ref('')

const filters = ref({
  difficulty: '',
  genre: '',
  key: '',
  sort: 'title',
})

// Methods
const fetchSongs = async () => {
  loading.value = true

  try {
    let endpoint = `/api/songs?page=${currentPage.value}&limit=12`

    if (currentQuery.value) {
      endpoint = `/api/songs/search?q=${encodeURIComponent(currentQuery.value)}&page=${currentPage.value}&limit=12`
    }

    const response = await $fetch(endpoint)

    songs.value = response
  } catch (error) {
    console.error('Error fetching songs:', error)
  } finally {
    loading.value = false
  }
}

const performSearch = async () => {
  currentQuery.value = searchQuery.value.trim()
  currentPage.value = 1
  await fetchSongs()
}

const applyFilters = () => {
  currentPage.value = 1
  fetchSongs()
}

const clearFilters = () => {
  filters.value = {
    difficulty: '',
    genre: '',
    key: '',
    sort: 'title',
  }

  searchQuery.value = ''
  currentQuery.value = ''
  currentPage.value = 1
  fetchSongs()
}

const changePage = (page: number) => {
  if (page < 1) return
  currentPage.value = page
  fetchSongs()
}

const viewSong = (id: string) => {
  navigateTo(`/song/${id}`)
}

// Initialize from URL query
onMounted(async () => {
  const route = useRoute()
  const urlQuery = route.query.q as string

  if (urlQuery) {
    searchQuery.value = urlQuery
    currentQuery.value = urlQuery
  }

  await fetchSongs()
})
</script>
