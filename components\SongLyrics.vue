<template>
  <div class="song-lyrics">
    <!-- Song Header -->
    <div class="mb-6 rounded-lg border bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
      <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 class="mb-1 text-2xl font-bold text-gray-900 md:text-3xl">
            {{ song.title }}
          </h1>
          <p class="text-lg text-gray-600">
            {{ song.artist }}
          </p>
          <div class="mt-2 flex flex-wrap gap-2">
            <span class="rounded-full bg-indigo-100 px-2 py-1 text-xs font-medium text-indigo-800">
              {{ song.genreThai }}
            </span>
            <span
              v-if="song.description"
              class="text-sm text-gray-500"
            >
              {{ song.description }}
            </span>
          </div>
        </div>

        <div class="flex flex-wrap gap-2">
          <span class="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
            Key: {{ currentKey }}
          </span>
          <span class="rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
            {{ song.difficultyThai }}
          </span>
          <span
            v-if="song.capo > 0"
            class="rounded-full bg-yellow-100 px-3 py-1 text-sm font-medium text-yellow-800"
          >
            Capo {{ song.capo }}
          </span>
          <div class="flex items-center gap-2">
            <button
              class="rounded-full bg-purple-100 px-2 py-1 text-sm font-medium text-purple-800 hover:bg-purple-200"
              title="ลดคีย์"
              @click="transposeDown"
            >
              ♭
            </button>
            <span class="text-xs text-gray-600">Transpose</span>
            <button
              class="rounded-full bg-purple-100 px-2 py-1 text-sm font-medium text-purple-800 hover:bg-purple-200"
              title="เพิ่มคีย์"
              @click="transposeUp"
            >
              ♯
            </button>
            <button
              class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600 hover:bg-gray-200"
              title="รีเซ็ต"
              @click="resetTranspose"
            >
              Reset
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Song Info -->
    <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <div class="rounded-lg border bg-white p-4">
        <div class="text-sm text-gray-600">
          Tempo
        </div>
        <div class="text-lg font-semibold">
          {{ song.tempo }} BPM
        </div>
      </div>
      <div class="rounded-lg border bg-white p-4">
        <div class="text-sm text-gray-600">
          Time Signature
        </div>
        <div class="text-lg font-semibold">
          {{ song.timeSignature }}
        </div>
      </div>
      <div class="rounded-lg border bg-white p-4">
        <div class="text-sm text-gray-600">
          Strumming
        </div>
        <div class="font-mono text-lg font-semibold">
          {{ song.strummingPattern || 'D-D-U-U-D-U' }}
        </div>
      </div>
      <div class="rounded-lg border bg-white p-4">
        <div class="text-sm text-gray-600">
          Total Chords
        </div>
        <div class="text-lg font-semibold">
          {{ song.chords.length }}
        </div>
      </div>
    </div>

    <!-- Chords Used -->
    <div class="mb-6 rounded-lg border bg-white p-4">
      <h3 class="mb-3 text-lg font-semibold text-gray-900">
        คอร์ดที่ใช้
      </h3>
      <div class="flex flex-wrap gap-2">
        <button
          v-for="chord in transposedChords"
          :key="chord"
          class="cursor-pointer rounded-lg bg-blue-100 px-4 py-2 font-medium text-blue-800 transition-colors hover:bg-blue-200"
          @click="showChordDiagram(chord)"
        >
          {{ chord }}
        </button>
      </div>
    </div>

    <!-- Controls -->
    <div class="mb-6 flex flex-wrap gap-4">
      <button
        class="rounded-lg bg-gray-100 px-4 py-2 text-gray-800 transition-colors hover:bg-gray-200"
        @click="toggleChords"
      >
        {{ showChords ? 'ซ่อนคอร์ด' : 'แสดงคอร์ด' }}
      </button>

      <button
        class="rounded-lg bg-gray-100 px-4 py-2 text-gray-800 transition-colors hover:bg-gray-200"
        @click="toggleAutoScroll"
      >
        {{ autoScroll ? 'หยุด Auto Scroll' : 'เริ่ม Auto Scroll' }}
      </button>

      <div class="flex items-center gap-2">
        <label class="text-sm text-gray-600">Font Size:</label>
        <select
          v-model="fontSize"
          class="rounded border border-gray-300 px-2 py-1 text-sm"
        >
          <option value="text-sm">
            เล็ก
          </option>
          <option value="text-base">
            ปกติ
          </option>
          <option value="text-lg">
            ใหญ่
          </option>
          <option value="text-xl">
            ใหญ่มาก
          </option>
        </select>
      </div>

      <div class="flex items-center gap-2">
        <label class="text-sm text-gray-600">Chord Style:</label>
        <select
          v-model="chordDisplayMode"
          class="rounded border border-gray-300 px-2 py-1 text-sm"
        >
          <option value="above">
            ข้างบน
          </option>
          <option value="inline">
            ในบรรทัด
          </option>
          <option value="both">
            ทั้งคู่
          </option>
        </select>
      </div>
    </div>

    <!-- Lyrics -->
    <div
      class="rounded-lg border bg-white p-6"
      :class="fontSize"
    >
      <div
        v-for="(section, sectionIndex) in song.lyrics"
        :key="sectionIndex"
        class="mb-8 last:mb-0"
      >
        <!-- Section Header -->
        <!-- <h3 class="mb-4 border-b border-blue-200 pb-2 text-lg font-bold text-blue-600">
          {{ section.section }}
        </h3> -->
        <hr class="mb-4 border border-blue-200" />

        <!-- Section Lines -->
        <div class="space-y-2">
          <div
            v-for="(line, lineIndex) in section.lines"
            :key="lineIndex"
            class="line-container relative"
            style="line-height: 2.5em; min-height: 2.5em;"
          >
            <template v-if="showChords">
              <!-- แสดงคอร์ดตามโหมดที่เลือก -->
              <div v-if="chordDisplayMode === 'above' || chordDisplayMode === 'both'">
                <div
                  v-if="line.chordsWithPositions?.length || line.chords?.length"
                  class="leading-tight"
                  @click="handleChordClick"
                  v-html="renderChordsAboveLyrics(line)"
                />
                <div class="lyrics-line mt-1 leading-relaxed text-gray-800">
                  {{ line.text || '' }}
                </div>
              </div>

              <div v-else-if="chordDisplayMode === 'inline'">
                <div
                  v-if="line.chordsWithPositions?.length"
                  class="lyrics-line relative text-gray-800"
                  style="line-height: 2.5em;"
                  @click="handleChordClick"
                  v-html="renderInlineChords(line)"
                />
                <div
                  v-else-if="line.chords?.length && !line.text"
                  class="chords-only mb-2"
                >
                  <span
                    v-for="(chord, chordIndex) in line.chords"
                    :key="chordIndex"
                    class="chord-span mr-4 cursor-pointer font-bold text-blue-600 hover:text-blue-800"
                    @click="showChordDiagram(transposeChord(chord))"
                  >
                    {{ transposeChord(chord) }}
                  </span>
                </div>
                <div
                  v-else
                  class="lyrics-line text-gray-800"
                  style="line-height: 2.5em;"
                >
                  {{ line.text || '' }}
                </div>
              </div>
            </template>
            <template v-else>
              <!-- แสดงเฉพาะเนื้อเพลง -->
              <div class="lyrics-line leading-relaxed text-gray-800">
                {{ line.text || (line.chords?.length ? `[${line.chords.join(' - ')}]` : '') }}
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- Tags -->
    <div
      v-if="song.tags?.length"
      class="mt-6 rounded-lg border bg-white p-4"
    >
      <h3 class="mb-3 text-lg font-semibold text-gray-900">
        แท็ก
      </h3>
      <div class="flex flex-wrap gap-2">
        <span
          v-for="tag in song.tags"
          :key="tag"
          class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-700"
        >
          {{ tag }}
        </span>
      </div>
    </div>

    <!-- Chord Diagram Modal -->
    <div
      v-if="selectedChord"
      class="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black"
      @click="closeChordDiagram"
    >
      <div
        class="mx-4 w-full max-w-sm rounded-lg bg-white p-6"
        @click.stop
      >
        <div class="mb-4 flex items-center justify-between">
          <h3 class="text-xl font-bold">
            คอร์ด {{ selectedChord }}
          </h3>
          <button
            class="text-gray-500 hover:text-gray-700"
            @click="closeChordDiagram"
          >
            ✕
          </button>
        </div>

        <div class="text-center text-gray-600">
          <p>แผนภาพคอร์ด {{ selectedChord }}</p>
          <p class="mt-2 text-sm">
            กำลังพัฒนา...
          </p>
          <!-- ที่นี่จะเป็น ChordDiagram component -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ChordPosition {
  chord: string
  position: number
}

interface LyricLine {
  text: string
  chords: string[]
  chordsWithPositions?: ChordPosition[]
}

interface LyricSection {
  section: string
  lines: LyricLine[]
}

interface Song {
  id: string
  title: string
  artist: string
  genre: string
  genreThai: string
  difficulty: string
  difficultyThai: string
  key: string
  tempo: number
  timeSignature: string
  capo: number
  strummingPattern: string
  chords: string[]
  lyrics: LyricSection[]
  tags: string[]
  description: string
  youtubeId: string
}

interface Props {
  song: Song
}

// Props
const props = defineProps<Props>()

// Reactive data
const showChords = ref(true)
const autoScroll = ref(false)
const fontSize = ref('text-base')
const chordDisplayMode = ref('above') // above, inline, both
const selectedChord = ref('')
const transposeSteps = ref(0)

// Computed
const currentKey = computed(() => {
  return transposeChord(props.song.key)
})

const transposedChords = computed(() => {
  return props.song.chords.map((chord) => transposeChord(chord))
})

// Methods
const toggleChords = () => {
  showChords.value = !showChords.value
}

const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value

  if (autoScroll.value) {
    startAutoScroll()
  } else {
    stopAutoScroll()
  }
}

const showChordDiagram = (chord: string) => {
  selectedChord.value = chord
}

const closeChordDiagram = () => {
  selectedChord.value = ''
}

// Transpose functions
const chromaticScale = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
const flatScale = ['C', 'Db', 'D', 'Eb', 'E', 'F', 'Gb', 'G', 'Ab', 'A', 'Bb', 'B']

const transposeChord = (chord: string): string => {
  if (!chord || transposeSteps.value === 0) return chord

  const chordMatch = chord.match(/^([A-G][#b]?)(.*)$/)
  if (!chordMatch) return chord

  const [, rootNote, quality] = chordMatch

  let noteIndex = chromaticScale.indexOf(rootNote)

  if (noteIndex === -1) {
    noteIndex = flatScale.indexOf(rootNote)
  }

  if (noteIndex === -1) return chord

  let newIndex = (noteIndex + transposeSteps.value) % 12
  if (newIndex < 0) newIndex += 12

  const useFlats = rootNote.includes('b') || ['F', 'Bb', 'Eb', 'Ab', 'Db', 'Gb'].includes(rootNote)
  const newNote = useFlats ? flatScale[newIndex] : chromaticScale[newIndex]

  return newNote + quality
}

const transposeUp = () => {
  transposeSteps.value = (transposeSteps.value + 1) % 12
}

const transposeDown = () => {
  transposeSteps.value = (transposeSteps.value - 1 + 12) % 12
}

const resetTranspose = () => {
  transposeSteps.value = 0
}

// Render functions
const renderChordsAboveLyrics = (line: LyricLine): string => {
  if (!line.chordsWithPositions?.length && !line.chords?.length) {
    return '&nbsp;'
  }

  const text = line.text || ''

  // ถ้ามีเฉพาะคอร์ดไม่มีเนื้อเพลง
  if (!text && line.chords?.length) {
    return line.chords
      .map((chord) => `<span class="cursor-pointer font-bold text-blue-600 hover:text-blue-800 mr-4" data-chord="${transposeChord(chord)}">${transposeChord(chord)}</span>`)
      .join('')
  }

  // ถ้ามี chordsWithPositions ใช้ตำแหน่งที่แน่นอน
  if (line.chordsWithPositions?.length) {
    const sortedChords = [...line.chordsWithPositions].sort((a, b) => a.position - b.position)
    let result = ''
    let lastPosition = 0

    sortedChords.forEach((chordPos) => {
      const {
        chord, position,
      } = chordPos

      const transposedChord = transposeChord(chord)

      // เพิ่มช่องว่างจนถึงตำแหน่งคอร์ด
      const spaces = Math.max(0, position - lastPosition)

      result += Array(spaces).fill('&nbsp;').join('')

      // เพิ่มคอร์ด
      result += `<span class="cursor-pointer font-bold text-blue-600 hover:text-blue-800 ml-[-15px]" data-chord="${transposedChord}">${transposedChord}</span>`

      // อัพเดทตำแหน่งสุดท้าย
      lastPosition = position + transposedChord.length
    })

    return result || '&nbsp;'
  }

  return '&nbsp;'
}

const renderInlineChords = (line: LyricLine): string => {
  if (!line.text) {
    return line.chords?.length
      ? line.chords.map((chord) => `<span class="chord-span cursor-pointer font-bold text-blue-600 hover:text-blue-800 mr-2" data-chord="${transposeChord(chord)}">[${transposeChord(chord)}]</span>`).join('')
      : ''
  }

  if (!line.chordsWithPositions?.length) {
    return line.text
  }

  const text = line.text
  const sortedChords = [...line.chordsWithPositions].sort((a, b) => a.position - b.position)

  let result = ''
  let lastPosition = 0

  sortedChords.forEach((chordPos) => {
    const {
      chord, position,
    } = chordPos

    const transposedChord = transposeChord(chord)

    // เพิ่มข้อความก่อนคอร์ด พร้อมกับ wrapper สำหรับ positioning
    const textBefore = text.slice(lastPosition, position)

    if (textBefore) {
      result += `<span class="lyrics-text">${textBefore}</span>`
    }

    // เพิ่มคอร์ดที่จะแสดงด้านบนของเนื้อเพลง
    result += `<span class="chord-container relative inline-block" style="min-width: 1ch;">
      <span class="chord-above absolute cursor-pointer font-bold text-blue-600 hover:text-blue-800 text-sm whitespace-nowrap"
            style="top: -2em; left: 0; z-index: 10; transform: translateY(0.2em);"
            data-chord="${transposedChord}">${transposedChord}</span>
      <span class="lyrics-text">${text.charAt(position) || '\u00A0'}</span>
    </span>`

    lastPosition = position + 1
  })

  // เพิ่มข้อความที่เหลือ
  const remainingText = text.slice(lastPosition)

  if (remainingText) {
    result += `<span class="lyrics-text">${remainingText}</span>`
  }

  return result
}

const handleChordClick = (event: Event) => {
  const target = event.target as HTMLElement

  if (target && target.hasAttribute('data-chord')) {
    const chord = target.getAttribute('data-chord')

    if (chord) {
      showChordDiagram(chord)
    }
  }
}

// Auto scroll
let scrollInterval: NodeJS.Timeout | null = null

const startAutoScroll = () => {
  scrollInterval = setInterval(() => {
    window.scrollBy(0, 1)
  }, 50)
}

const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval)
    scrollInterval = null
  }
}

// Cleanup
onUnmounted(() => {
  stopAutoScroll()
})
</script>
