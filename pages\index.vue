<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <header class="border-b bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">
              คอร์ด MHalong
            </h1>
          </div>
          <nav class="hidden space-x-8 md:flex">
            <NuxtLink
              to="/"
              class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              หน้าแรก
            </NuxtLink>
            <NuxtLink
              to="/search"
              class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              ค้นหาคอร์ด
            </NuxtLink>
            <NuxtLink
              to="/songs"
              class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              เพลงและคอร์ด
            </NuxtLink>
          </nav>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="py-20">
      <div class="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
        <h2 class="mb-6 text-4xl font-bold text-gray-900 md:text-6xl">
          เพลงและคอร์ดกีตาร์
        </h2>
        <p class="mx-auto mb-8 max-w-3xl text-xl text-gray-600">
          ค้นหาเพลงพร้อมเนื้อเพลงและคอร์ดกีตาร์ที่ละเอียด
          เหมาะสำหรับการฝึกซ้อมและเรียนรู้เพลงใหม่ๆ
        </p>

        <!-- Search Bar -->
        <div class="mx-auto mb-12 max-w-md">
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="ค้นหาเพลง ศิลปิน หรือคอร์ด..."
              class="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 pr-4 pl-12 text-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none"
              @keyup.enter="searchSongs"
            />
            <div class="absolute inset-y-0 left-0 flex items-center pl-4">
              <Icon
                name="lucide:search"
                class="h-5 w-5 text-gray-400"
              />
            </div>
          </div>
          <button
            class="mt-4 w-full rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
            @click="searchSongs"
          >
            ค้นหาเพลง
          </button>
        </div>
      </div>
    </section>

    <!-- Popular Songs Section -->
    <section class="bg-white py-16">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <h3 class="mb-12 text-center text-3xl font-bold text-gray-900">
          เพลงยอดนิยม
        </h3>

        <div
          v-if="loading"
          class="flex justify-center"
        >
          <div class="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600" />
        </div>

        <div
          v-else
          class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
        >
          <SongCard
            v-for="song in popularSongs"
            :key="song.id"
            :song="song"
            @click="viewSong(song.id)"
          />
        </div>

        <div class="mt-12 space-x-4 text-center">
          <NuxtLink
            to="/songs"
            class="inline-flex items-center rounded-md border border-transparent bg-blue-100 px-6 py-3 text-base font-medium text-blue-600 transition-colors hover:bg-blue-200"
          >
            ดูเพลงทั้งหมด
            <Icon
              name="lucide:music"
              class="ml-2 h-5 w-5"
            />
          </NuxtLink>
          <NuxtLink
            to="/search"
            class="inline-flex items-center rounded-md border border-transparent bg-green-100 px-6 py-3 text-base font-medium text-green-600 transition-colors hover:bg-green-200"
          >
            ค้นหาคอร์ด
            <Icon
              name="lucide:search"
              class="ml-2 h-5 w-5"
            />
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="bg-gray-50 py-16">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <h3 class="mb-12 text-center text-3xl font-bold text-gray-900">
          ฟีเจอร์เด่น
        </h3>

        <div class="grid grid-cols-1 gap-8 md:grid-cols-3">
          <div class="text-center">
            <div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
              <Icon
                name="lucide:music"
                class="h-8 w-8 text-blue-600"
              />
            </div>
            <h4 class="mb-2 text-xl font-semibold text-gray-900">
              เนื้อเพลงพร้อมคอร์ด
            </h4>
            <p class="text-gray-600">
              แสดงเนื้อเพลงพร้อมคอร์ดกีตาร์ที่ตำแหน่งที่ถูกต้อง เหมาะสำหรับการฝึกซ้อม
            </p>
          </div>

          <div class="text-center">
            <div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <Icon
                name="lucide:search"
                class="h-8 w-8 text-green-600"
              />
            </div>
            <h4 class="mb-2 text-xl font-semibold text-gray-900">
              ค้นหาเพลงง่าย
            </h4>
            <p class="text-gray-600">
              ค้นหาเพลงตามชื่อ ศิลปิน ประเภทเพลง หรือคอร์ดที่ใช้
            </p>
          </div>

          <div class="text-center">
            <div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
              <Icon
                name="lucide:play-circle"
                class="h-8 w-8 text-purple-600"
              />
            </div>
            <h4 class="mb-2 text-xl font-semibold text-gray-900">
              วิดีโอประกอบ
            </h4>
            <p class="text-gray-600">
              มีวิดีโอ YouTube ประกอบการเรียนรู้ พร้อมเทคนิคการเล่น
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// SEO
useHead({
  title: 'คอร์ด MHalong - เพลงและคอร์ดกีตาร์',
  meta: [
    {
      name: 'description',
      content: 'ค้นหาเพลงพร้อมเนื้อเพลงและคอร์ดกีตาร์ที่ละเอียด เหมาะสำหรับการฝึกซ้อมและเรียนรู้เพลงใหม่ๆ',
    },
  ],
})

// Reactive data
const searchQuery = ref('')
const loading = ref(true)
const popularSongs = ref<any[]>([])

// Methods
const searchSongs = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/songs?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

const viewSong = (id: string) => {
  navigateTo(`/song/${id}`)
}

// Fetch popular songs on mount
onMounted(async () => {
  try {
    const {
      data,
    } = await $fetch('/api/songs?limit=6')

    popularSongs.value = data
  } catch (error) {
    console.error('Error fetching popular songs:', error)
  } finally {
    loading.value = false
  }
})
</script>
