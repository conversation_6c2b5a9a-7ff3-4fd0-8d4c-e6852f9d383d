export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const searchQuery = (query.q as string) || ''
  const page = Number.parseInt(query.page as string) || 1
  const limit = Number.parseInt(query.limit as string) || 20

  try {
    // เรียก API จาก dochord.com
    const dochordUrl = `https://www.dochord.com/wp-json/wp/v2/posts?search=${encodeURIComponent(searchQuery)}&_embed&per_page=${limit}&page=${page}`

    const response = await $fetch(dochordUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    })

    // แปลงข้อมูลจาก dochord.com ให้เป็นรูปแบบที่เราต้องการ
    const transformedSongs = response.map((post: any) => {
      const content = post.content?.rendered || ''
      const title = post.title?.rendered || ''
      const excerpt = post.excerpt?.rendered || ''

      // ดึงข้อมูลศิลปินจาก _embedded.wp:term
      const artistInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
        termGroup.some((term: any) => term.taxonomy === 'artist'),
      )?.find((term: any) => term.taxonomy === 'artist')

      // ดึงรูปภาพจาก _embedded.wp:featuredmedia
      const featuredImage = post._embedded?.['wp:featuredmedia']?.[0]?.source_url || ''

      // ดึงหมวดหมู่จาก _embedded.wp:term
      const categoryInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
        termGroup.some((term: any) => term.taxonomy === 'category'),
      )?.find((term: any) => term.taxonomy === 'category')

      return {
        id: post.id.toString(),
        title: title.replace(/<[^>]*>/g, ''),
        artist: artistInfo?.name || 'Unknown Artist',
        genre: categoryInfo?.name || 'Pop',
        genreThai: categoryInfo?.name || 'ป็อป',
        difficulty: 'beginner',
        difficultyThai: 'ง่าย',
        key: extractKeyFromContent(content) || 'C',
        tempo: 120,
        timeSignature: '4/4',
        capo: extractCapoFromContent(content) || 0,
        strummingPattern: 'D-D-U-D-U',
        chords: extractChordsFromContent(content),
        tags: ['dochord', 'เพลงไทย'],
        description: excerpt.replace(/<[^>]*>/g, '').substring(0, 200) + '...',
        youtubeId: extractYouTubeId(content) || '',
        duration: '3:30',
        releaseYear: new Date(post.date).getFullYear(),
        album: 'Single',
        url: post.link,
        image: featuredImage,
        content: content,
        slug: post.slug,
        date: post.date,
      }
    })

    const totalCount = Number.parseInt(event.node.res.getHeader('X-WP-Total') as string) || transformedSongs.length
    const totalPages = Math.ceil(totalCount / limit)

    return {
      success: true,
      query: searchQuery,
      data: transformedSongs,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  } catch (error) {
    console.error('Error fetching from dochord.com:', error)

    return {
      success: false,
      error: 'Failed to fetch from dochord.com',
      query: searchQuery,
      data: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalCount: 0,
        limit,
        hasNext: false,
        hasPrev: false,
      },
    }
  }
})

function extractKeyFromContent(content: string): string {
  const keyPatterns = [
    /คีย์\s*(?::\s*)?([A-G][#b]?m?)/i,
    /key\s*(?::\s*)?([A-G][#b]?m?)/i,
    /ทำนอง\s*([A-G][#b]?m?)/i,
  ]

  for (const pattern of keyPatterns) {
    const match = content.match(pattern)

    if (match) {
      return match[1]
    }
  }

  return 'C'
}

function extractCapoFromContent(content: string): number {
  const capoPatterns = [
    /capo\s*(?::\s*)?(\d+)/i,
    /คาโป\s*(?::\s*)?(\d+)/,
    /เฟรต\s*(\d+)/,
  ]

  for (const pattern of capoPatterns) {
    const match = content.match(pattern)

    if (match) {
      return Number.parseInt(match[1])
    }
  }

  return 0
}

function extractChordsFromContent(content: string): string[] {
  const chordPattern = /\b([A-G][#b]?(?:m|maj|min|sus|add|dim|aug)?\d*)\b/g
  const matches = content.match(chordPattern) || []

  const uniqueChords = [...new Set(matches)]
    .filter((chord) => /^[A-G][#b]?(?:m|maj|min|sus|add|dim|aug)?\d*$/.test(chord))
    .slice(0, 10)

  return uniqueChords.length > 0 ? uniqueChords : ['C', 'G', 'Am', 'F']
}

function extractYouTubeId(content: string): string {
  const youtubePatterns = [
    /youtube\.com\/watch\?v=([\w-]+)/,
    /youtu\.be\/([\w-]+)/,
    /youtube\.com\/embed\/([\w-]+)/,
  ]

  for (const pattern of youtubePatterns) {
    const match = content.match(pattern)

    if (match) {
      return match[1]
    }
  }

  return ''
}
