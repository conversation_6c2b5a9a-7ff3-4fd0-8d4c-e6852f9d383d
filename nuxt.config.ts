export default defineNuxtConfig({
  modules: ['@finema/core'],
  ssr: true, // Enable server-side rendering for better performance and SEO
  devtools: {
    enabled: true,
  },
  app: {
    head: {
      meta: [
        {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1, user-scalable=no',
        },
        {
          name: 'theme-color',
          content: '#14B8A6',
        },
        {
          name: 'application-name',
          content: 'คอร์ด MHalong',
        },
        {
          name: 'apple-mobile-web-app-title',
          content: 'คอร์ด MHalong',
        },
        {
          name: 'mobile-web-app-capable',
          content: 'yes',
        },
        {
          name: 'apple-mobile-web-app-capable',
          content: 'yes',
        },
        {
          name: 'description',
          content: 'คอร์ด MHalong - แอปพลิเคชันสำหรับดูคอร์ดกีตาร์และดนตรี ค้นหา เรียนรู้คอร์ดกีตาร์พร้อมแผนภาพและตำแหน่งนิ้วกดที่ละเอียด',
        },
        {
          name: 'keywords',
          content: 'คอร์ดกีตาร์, คอร์ดดนตรี, แผนภาพคอร์ด, แท็บกีตาร์, ค้นหาคอร์ด, ทฤษฎีดนตรี, เรียนกีตาร์, progression คอร์ด, ตำแหน่งนิ้วกด',
        },
        {
          property: 'og:type',
          content: 'website',
        },
        {
          property: 'og:site_name',
          content: 'คอร์ด MHalong',
        },
        {
          property: 'og:title',
          content: 'คอร์ด MHalong - ดูคอร์ดกีตาร์',
        },
        {
          property: 'og:description',
          content: 'แอปพลิเคชันสำหรับดูคอร์ดกีตาร์และดนตรี ค้นหา เรียนรู้คอร์ดกีตาร์พร้อมแผนภาพและตำแหน่งนิ้วกดที่ละเอียด',
        },
        {
          name: 'twitter:title',
          content: 'คอร์ด MHalong - ดูคอร์ดกีตาร์',
        },
        {
          name: 'twitter:description',
          content: 'แอปพลิเคชันสำหรับดูคอร์ดกีตาร์และดนตรี ค้นหา เรียนรู้คอร์ดกีตาร์พร้อมแผนภาพและตำแหน่งนิ้วกด',
        },
      ],
      link: [
        {
          rel: 'manifest',
          href: '/manifest.json',
        },
      ],
    },

  },
  css: ['~/assets/css/main.css'],
  sourcemap: {
    client: 'hidden',
  },
  compatibilityDate: '2025-05-15',
  vite: {
    optimizeDeps: {
      include: [
        '@vueuse/core',
        '@dicebear/collection',
        '@dicebear/core',
      ],
    },
  },
  core: {},
})
